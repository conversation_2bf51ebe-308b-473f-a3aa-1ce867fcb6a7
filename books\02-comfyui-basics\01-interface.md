# 2.1 界面操作基础

## 🎯 学习目标

全面掌握ComfyUI的界面操作，包括节点管理、工作流操作、队列系统等核心功能。

## 🖥️ ComfyUI界面布局

### 主界面组成

ComfyUI的Web界面主要包含以下区域：

```
┌─────────────────────────────────────────────────────────┐
│  菜单栏 (Menu Bar)                                        │
├─────────────────────────────────────────────────────────┤
│  工具栏 (Toolbar)                                         │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                                                         │
│              工作区 (Canvas)                              │
│                                                         │
│                                                         │
├─────────────────────────────────────────────────────────┤
│  侧边栏 (Sidebar)                                         │
│  - 节点库                                                │
│  - 队列管理                                              │
│  - 历史记录                                              │
└─────────────────────────────────────────────────────────┘
```

### 1. 菜单栏功能

#### 文件操作
- **Load**：加载工作流文件(.json)
- **Save**：保存当前工作流
- **Clear**：清空当前工作区
- **Load Default**：加载默认工作流

#### 视图控制
- **Reset View**：重置视图到默认位置
- **Fit View**：自适应显示所有节点

#### 设置选项
- **Settings**：打开设置面板
- **Extensions**：扩展管理
- **About**：关于信息

### 2. 工具栏功能

```
[Queue Prompt] [Extra Options] [Clear] [Load] [Save] [Settings]
```

- **Queue Prompt**：将当前工作流加入执行队列
- **Extra Options**：额外选项（批量生成等）
- **Clear**：清空工作区
- **Load/Save**：快速加载/保存
- **Settings**：快速设置

### 3. 工作区操作

#### 视图控制
- **平移**：鼠标中键拖拽或空格+左键拖拽
- **缩放**：鼠标滚轮或Ctrl+滚轮
- **框选**：Ctrl+左键拖拽选择多个节点
- **全选**：Ctrl+A选择所有节点

#### 网格和对齐
- **网格显示**：帮助节点对齐
- **吸附功能**：节点自动对齐到网格
- **对齐工具**：多选节点后可以对齐

## 🔧 节点操作详解

### 添加节点

#### 方法1：右键菜单
```
右键点击空白区域 → Add Node → 选择分类 → 选择具体节点
```

#### 方法2：双击添加
```
双击空白区域 → 搜索节点名称 → 回车确认
```

#### 方法3：快捷键
```
Tab键 → 打开节点搜索 → 输入名称 → 回车
```

### 节点连接

#### 基本连接
1. **输出到输入**：从输出接口拖拽到输入接口
2. **自动连接**：拖拽节点到另一个节点上自动连接
3. **批量连接**：选择多个节点后批量连接

#### 连接规则
- **类型匹配**：只能连接兼容的数据类型
- **单输入**：一个输入只能连接一个输出
- **多输出**：一个输出可以连接多个输入

#### 断开连接
- **单个断开**：右键点击连接线选择断开
- **批量断开**：选择节点后按Delete键
- **清空输入**：右键点击输入接口选择断开

### 节点编辑

#### 参数修改
```python
# 节点参数类型示例
{
    "text": "输入文本",           # 文本输入
    "seed": 42,                # 数值输入
    "steps": 20,               # 滑块输入
    "sampler_name": "euler",   # 下拉选择
    "width": 512,              # 数值输入
    "height": 512              # 数值输入
}
```

#### 节点属性
- **标题编辑**：双击节点标题可以重命名
- **颜色标记**：右键节点可以设置颜色
- **分组管理**：可以将节点分组管理
- **注释添加**：为节点添加说明注释

### 节点管理

#### 复制和粘贴
```
Ctrl+C  # 复制选中节点
Ctrl+V  # 粘贴节点
Ctrl+D  # 复制节点（就地复制）
```

#### 删除操作
```
Delete     # 删除选中节点
Backspace  # 删除选中节点
```

#### 撤销重做
```
Ctrl+Z  # 撤销操作
Ctrl+Y  # 重做操作
```

## 📁 工作流管理

### 保存工作流

#### 保存格式
ComfyUI支持多种保存格式：

1. **JSON格式**：标准工作流格式
```json
{
  "1": {
    "class_type": "CheckpointLoaderSimple",
    "inputs": {
      "ckpt_name": "model.safetensors"
    }
  },
  "2": {
    "class_type": "CLIPTextEncode",
    "inputs": {
      "text": "a beautiful landscape",
      "clip": ["1", 1]
    }
  }
}
```

2. **PNG格式**：将工作流嵌入到生成的图像中
3. **API格式**：用于程序化调用的格式

#### 保存操作
```
方法1：菜单栏 → Save → 选择保存位置
方法2：Ctrl+S → 快速保存
方法3：工具栏 → Save按钮
```

### 加载工作流

#### 加载方式
1. **文件加载**：Load按钮选择JSON文件
2. **拖拽加载**：直接拖拽文件到界面
3. **图像加载**：拖拽包含工作流的PNG图像
4. **URL加载**：从网络URL加载工作流

#### 加载选项
- **替换当前**：完全替换当前工作流
- **合并加载**：与当前工作流合并
- **追加加载**：在当前工作流基础上追加

### 工作流模板

#### 创建模板
```python
# 基础txt2img模板
basic_txt2img_template = {
    "checkpoint_loader": "CheckpointLoaderSimple",
    "text_encode_positive": "CLIPTextEncode", 
    "text_encode_negative": "CLIPTextEncode",
    "sampler": "KSampler",
    "vae_decode": "VAEDecode",
    "save_image": "SaveImage"
}
```

#### 模板管理
- **保存模板**：将常用工作流保存为模板
- **模板分类**：按用途分类管理模板
- **快速应用**：一键应用模板到新工作流

## 🔄 队列系统

### 队列管理界面

队列面板显示：
```
┌─────────────────────────────────┐
│ Queue (2)                       │
├─────────────────────────────────┤
│ ▶ Prompt 1 [Running]           │
│ ⏸ Prompt 2 [Pending]           │
│ ✓ Prompt 3 [Completed]         │
├─────────────────────────────────┤
│ [Clear Queue] [Pause] [Resume]  │
└─────────────────────────────────┘
```

### 队列操作

#### 添加到队列
```
方法1：点击"Queue Prompt"按钮
方法2：Ctrl+Enter快捷键
方法3：右键工作区选择"Queue Prompt"
```

#### 批量生成
```python
# 批量生成设置
batch_settings = {
    "batch_count": 5,        # 生成5张图像
    "seed_increment": True,  # 自动递增种子
    "save_prefix": "batch_", # 文件名前缀
}
```

#### 队列控制
- **暂停队列**：暂停当前执行
- **恢复队列**：恢复执行
- **清空队列**：清除所有待执行项目
- **删除单项**：删除特定队列项目

### 执行监控

#### 进度显示
- **当前步骤**：显示正在执行的步骤
- **进度条**：显示整体进度
- **时间估计**：预估剩余时间
- **资源使用**：显示GPU/内存使用情况

#### 错误处理
```python
# 常见错误类型
error_types = {
    "model_not_found": "模型文件未找到",
    "out_of_memory": "显存不足", 
    "invalid_input": "输入参数无效",
    "connection_error": "节点连接错误"
}
```

## ⌨️ 快捷键大全

### 基础操作
```
Ctrl+N      # 新建工作流
Ctrl+O      # 打开工作流
Ctrl+S      # 保存工作流
Ctrl+Z      # 撤销
Ctrl+Y      # 重做
Ctrl+A      # 全选
Delete      # 删除选中项
```

### 视图控制
```
Space+拖拽   # 平移视图
鼠标滚轮     # 缩放视图
Ctrl+0      # 重置视图
Ctrl+1      # 适应视图
F           # 聚焦到选中节点
```

### 节点操作
```
Tab         # 添加节点搜索
Ctrl+C      # 复制节点
Ctrl+V      # 粘贴节点
Ctrl+D      # 复制节点（就地）
Ctrl+G      # 节点分组
Ctrl+Shift+G # 取消分组
```

### 执行控制
```
Ctrl+Enter  # 队列执行
Escape      # 停止执行
F5          # 刷新界面
```

## 🎨 界面自定义

### 主题设置

ComfyUI支持多种主题：
- **Dark Theme**：深色主题（默认）
- **Light Theme**：浅色主题
- **Custom Theme**：自定义主题

### 布局调整

#### 面板管理
- **隐藏侧边栏**：更多工作区空间
- **调整面板大小**：拖拽面板边界
- **面板停靠**：将面板停靠到不同位置

#### 网格设置
```python
grid_settings = {
    "show_grid": True,      # 显示网格
    "grid_size": 20,        # 网格大小
    "snap_to_grid": True,   # 吸附到网格
    "grid_color": "#333333" # 网格颜色
}
```

### 性能优化

#### 渲染设置
- **节点简化**：在大型工作流中简化节点显示
- **连接线优化**：优化连接线渲染
- **动画减少**：减少界面动画提升性能

#### 内存管理
- **自动清理**：自动清理未使用的资源
- **缓存控制**：控制浏览器缓存大小
- **预览质量**：调整预览图像质量

## 🔧 实践练习

### 练习1：界面熟悉
1. 启动ComfyUI并熟悉界面布局
2. 尝试所有菜单选项
3. 练习视图控制操作
4. 熟悉快捷键使用

### 练习2：节点操作
1. 添加5个不同类型的节点
2. 练习节点连接和断开
3. 修改节点参数
4. 尝试节点复制和删除

### 练习3：工作流管理
1. 创建一个简单工作流
2. 保存为JSON格式
3. 清空工作区后重新加载
4. 尝试不同的保存格式

### 练习4：队列系统
1. 创建多个不同的提示
2. 将它们加入队列
3. 观察执行过程
4. 练习队列控制操作

## 📝 小结

掌握ComfyUI界面操作是高效使用的基础：

- **界面布局**：理解各个区域的功能
- **节点操作**：熟练的节点管理技能
- **工作流管理**：有效的工作流组织方法
- **队列系统**：批量处理和执行控制
- **快捷键**：提升操作效率的关键

熟练掌握这些基础操作将为后续的高级功能学习打下坚实基础。

## 🎯 检查清单

完成本节学习后，你应该能够：

- [ ] 熟练导航ComfyUI界面
- [ ] 快速添加和连接节点
- [ ] 有效管理工作流文件
- [ ] 使用队列系统批量处理
- [ ] 熟练使用常用快捷键
- [ ] 自定义界面布局和设置

---

**下一节**：[2.2 基础节点使用](./02-basic-nodes.md)
