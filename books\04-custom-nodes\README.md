# 第四章：自定义节点开发

## 🎯 学习目标

掌握ComfyUI自定义节点的开发技能，从简单节点到复杂功能，最终能够开发和发布自己的节点插件。

### 学习成果
- 掌握节点开发的基本流程
- 理解节点类型系统和接口设计
- 能够开发各种类型的自定义节点
- 掌握节点测试和调试技巧
- 学会打包和发布节点插件

## 📚 章节内容

### [4.1 简单节点开发](./01-simple-nodes.md)
- 节点开发环境搭建
- 第一个自定义节点
- 基础节点类型和接口
- 节点注册和加载机制
- 调试和测试方法

### [4.2 复杂节点开发](./02-complex-nodes.md)
- 高级节点功能实现
- 异步处理和进度回调
- 错误处理和异常管理
- 资源管理和内存优化
- 与外部库的集成

### [4.3 节点打包发布](./03-packaging.md)
- 项目结构和配置
- 依赖管理和环境兼容
- 文档编写和示例提供
- 版本控制和发布流程
- 社区分享和维护

## 🛠️ 开发环境搭建

### 基础环境

```bash
# 创建开发目录
mkdir comfyui-custom-nodes
cd comfyui-custom-nodes

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install torch torchvision torchaudio
pip install pillow numpy opencv-python
pip install pytest black flake8 mypy
```

### 开发工具配置

#### VSCode配置
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true
}
```

#### 项目结构
```
my-custom-nodes/
├── __init__.py
├── nodes/
│   ├── __init__.py
│   ├── basic_nodes.py
│   ├── image_nodes.py
│   └── utility_nodes.py
├── tests/
│   ├── __init__.py
│   ├── test_basic_nodes.py
│   └── test_image_nodes.py
├── examples/
│   ├── workflows/
│   └── images/
├── docs/
│   ├── README.md
│   └── api.md
├── requirements.txt
├── setup.py
└── pyproject.toml
```

## 🎯 节点开发基础

### 节点类基本结构

```python
from comfy.comfy_types import IO, ComfyNodeABC, InputTypeDict

class MyCustomNode(ComfyNodeABC):
    """自定义节点示例"""
    
    @classmethod
    def INPUT_TYPES(cls) -> InputTypeDict:
        return {
            "required": {
                "input_param": (IO.STRING, {"default": "default_value"}),
            },
            "optional": {
                "optional_param": (IO.INT, {"default": 10, "min": 1, "max": 100}),
            }
        }
    
    RETURN_TYPES = (IO.STRING,)
    RETURN_NAMES = ("output",)
    FUNCTION = "execute"
    CATEGORY = "custom/my_nodes"
    DESCRIPTION = "这是一个自定义节点示例"
    
    def execute(self, input_param: str, optional_param: int = 10):
        """节点执行逻辑"""
        result = f"处理结果: {input_param} with {optional_param}"
        return (result,)
```

### 节点注册机制

```python
# __init__.py
from .nodes.basic_nodes import MyCustomNode

NODE_CLASS_MAPPINGS = {
    "MyCustomNode": MyCustomNode,
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "MyCustomNode": "My Custom Node",
}

__all__ = ["NODE_CLASS_MAPPINGS", "NODE_DISPLAY_NAME_MAPPINGS"]
```

## 📊 数据类型系统

### 内置数据类型

```python
# ComfyUI内置数据类型
builtin_types = {
    # 基础类型
    "STRING": str,
    "INT": int, 
    "FLOAT": float,
    "BOOLEAN": bool,
    
    # 图像和媒体
    "IMAGE": torch.Tensor,      # [B, H, W, C] 格式
    "MASK": torch.Tensor,       # [B, H, W] 格式
    "LATENT": dict,             # {"samples": tensor}
    
    # AI模型相关
    "MODEL": object,            # 扩散模型
    "CLIP": object,             # CLIP文本编码器
    "VAE": object,              # VAE编解码器
    "CONDITIONING": list,       # 条件信息
    
    # 控制相关
    "CONTROL_NET": object,      # ControlNet模型
    "STYLE_MODEL": object,      # 风格模型
    
    # 特殊类型
    "COMBO": list,              # 下拉选择
    "*": object,                # 任意类型
}
```

### 自定义数据类型

```python
# 定义自定义数据类型
class CustomDataType:
    def __init__(self, data, metadata=None):
        self.data = data
        self.metadata = metadata or {}
    
    def __str__(self):
        return f"CustomDataType({self.data})"

# 在节点中使用
class CustomTypeNode(ComfyNodeABC):
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "custom_input": ("CUSTOM_TYPE", {}),
            }
        }
    
    RETURN_TYPES = ("CUSTOM_TYPE",)
    FUNCTION = "process"
    
    def process(self, custom_input):
        # 处理自定义类型数据
        result = CustomDataType(
            data=custom_input.data + "_processed",
            metadata={"processed": True}
        )
        return (result,)
```

## 🧪 开发最佳实践

### 1. 代码组织

```python
# 按功能模块组织代码
class ImageProcessingNode(ComfyNodeABC):
    """图像处理节点基类"""
    
    def __init__(self):
        super().__init__()
        self.device = self._get_device()
    
    def _get_device(self):
        """获取计算设备"""
        return torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    def _validate_image(self, image):
        """验证图像格式"""
        if not isinstance(image, torch.Tensor):
            raise ValueError("输入必须是torch.Tensor")
        if len(image.shape) != 4:
            raise ValueError("图像必须是4维张量 [B, H, W, C]")
        return True
    
    def _preprocess_image(self, image):
        """图像预处理"""
        # 确保图像在正确的设备上
        image = image.to(self.device)
        # 确保数据类型正确
        if image.dtype != torch.float32:
            image = image.float()
        return image
```

### 2. 错误处理

```python
class RobustNode(ComfyNodeABC):
    """具有健壮错误处理的节点"""
    
    def execute(self, input_data):
        try:
            # 输入验证
            self._validate_inputs(input_data)
            
            # 主要处理逻辑
            result = self._process_data(input_data)
            
            # 输出验证
            self._validate_outputs(result)
            
            return (result,)
            
        except ValueError as e:
            raise ValueError(f"输入数据错误: {str(e)}")
        except RuntimeError as e:
            raise RuntimeError(f"处理过程错误: {str(e)}")
        except Exception as e:
            raise Exception(f"未知错误: {str(e)}")
    
    def _validate_inputs(self, input_data):
        """输入验证"""
        if input_data is None:
            raise ValueError("输入数据不能为空")
    
    def _process_data(self, input_data):
        """数据处理逻辑"""
        # 实际处理逻辑
        return input_data
    
    def _validate_outputs(self, result):
        """输出验证"""
        if result is None:
            raise RuntimeError("处理结果为空")
```

### 3. 性能优化

```python
class OptimizedNode(ComfyNodeABC):
    """性能优化的节点"""
    
    def __init__(self):
        super().__init__()
        self._cache = {}
        self._device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    def execute(self, input_data, use_cache=True):
        # 缓存检查
        cache_key = self._get_cache_key(input_data)
        if use_cache and cache_key in self._cache:
            return self._cache[cache_key]
        
        # 内存管理
        with torch.cuda.device(self._device):
            torch.cuda.empty_cache()  # 清理GPU缓存
            
            # 处理逻辑
            result = self._process_with_optimization(input_data)
            
            # 缓存结果
            if use_cache:
                self._cache[cache_key] = result
            
            return result
    
    def _get_cache_key(self, input_data):
        """生成缓存键"""
        return hash(str(input_data))
    
    def _process_with_optimization(self, input_data):
        """优化的处理逻辑"""
        # 使用torch.no_grad()减少内存使用
        with torch.no_grad():
            # 批处理优化
            if isinstance(input_data, list):
                return self._batch_process(input_data)
            else:
                return self._single_process(input_data)
```

## 🔧 测试和调试

### 单元测试

```python
# tests/test_custom_nodes.py
import pytest
import torch
from nodes.basic_nodes import MyCustomNode

class TestMyCustomNode:
    def setup_method(self):
        """测试前准备"""
        self.node = MyCustomNode()
    
    def test_basic_functionality(self):
        """测试基本功能"""
        result = self.node.execute("test_input", 5)
        assert isinstance(result, tuple)
        assert len(result) == 1
        assert "test_input" in result[0]
    
    def test_input_validation(self):
        """测试输入验证"""
        with pytest.raises(ValueError):
            self.node.execute(None)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空字符串
        result = self.node.execute("", 1)
        assert result is not None
        
        # 测试极值
        result = self.node.execute("test", 100)
        assert result is not None

    @pytest.mark.parametrize("input_val,expected", [
        ("hello", "hello"),
        ("world", "world"),
        ("", ""),
    ])
    def test_parametrized(self, input_val, expected):
        """参数化测试"""
        result = self.node.execute(input_val, 1)
        assert expected in result[0]
```

### 集成测试

```python
# tests/test_integration.py
import json
from unittest.mock import Mock

class TestNodeIntegration:
    def test_workflow_integration(self):
        """测试节点在工作流中的集成"""
        # 模拟工作流环境
        workflow = {
            "1": {
                "class_type": "MyCustomNode",
                "inputs": {
                    "input_param": "test",
                    "optional_param": 10
                }
            }
        }
        
        # 执行测试
        node = MyCustomNode()
        result = node.execute("test", 10)
        
        assert result is not None
        assert isinstance(result[0], str)
```

## 📦 项目模板

### 完整的节点项目模板

```python
# my_custom_nodes/__init__.py
"""
ComfyUI自定义节点包
提供图像处理、文本处理等功能
"""

from .nodes import *

# 节点注册
NODE_CLASS_MAPPINGS = {
    "MyTextProcessor": MyTextProcessor,
    "MyImageFilter": MyImageFilter,
    "MyUtilityNode": MyUtilityNode,
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "MyTextProcessor": "Text Processor",
    "MyImageFilter": "Image Filter", 
    "MyUtilityNode": "Utility Node",
}

# 版本信息
__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

# Web目录（如果有前端组件）
WEB_DIRECTORY = "./web"

__all__ = [
    "NODE_CLASS_MAPPINGS", 
    "NODE_DISPLAY_NAME_MAPPINGS",
    "WEB_DIRECTORY"
]
```

## 🎯 学习路径

### 第1周：基础节点开发
- 环境搭建和工具配置
- 第一个简单节点
- 数据类型和接口理解
- 基础测试和调试

### 第2周：进阶功能开发
- 复杂数据处理节点
- 图像处理节点
- 异步和进度处理
- 性能优化技巧

### 第3周：项目化开发
- 完整项目结构
- 文档和示例编写
- 测试覆盖和质量保证
- 打包和发布准备

## 🚀 进入下一章

完成本章学习后，你将具备：

- 完整的节点开发技能
- 项目管理和质量保证能力
- 社区贡献和协作经验
- 为高级应用开发做好准备

准备好后，可以进入[第五章：高级应用与优化](../05-advanced/README.md)，学习更高级的应用技巧。

---

**学习提示**：
- 从简单节点开始，逐步增加复杂度
- 重视测试和文档，这是专业开发的标志
- 多参考优秀的开源节点项目
- 积极参与社区，获取反馈和建议
