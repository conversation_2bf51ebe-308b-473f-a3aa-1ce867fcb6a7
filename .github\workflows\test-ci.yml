# This is the GitHub Workflow that drives automatic full-GPU-enabled tests of all new commits to the master branch of ComfyUI
# Results are reported as checkmarks on the commits, as well as onto https://ci.comfy.org/
name: Full Comfy CI Workflow Runs
on:
  push:
    branches:
      - master
    paths-ignore:
      - 'app/**'
      - 'input/**'
      - 'output/**'
      - 'notebooks/**'
      - 'script_examples/**'
      - '.github/**'
      - 'web/**'
  workflow_dispatch:

jobs:
  test-stable:
    strategy:
      fail-fast: false
      matrix:
        # os: [macos, linux, windows]
        os: [macos, linux]
        python_version: ["3.9", "3.10", "3.11", "3.12"]
        cuda_version: ["12.1"]
        torch_version: ["stable"]
        include:
          - os: macos
            runner_label: [self-hosted, macOS]
            flags: "--use-pytorch-cross-attention"
          - os: linux
            runner_label: [self-hosted, Linux]
            flags: ""
          # - os: windows
          #   runner_label: [self-hosted, Windows]
          #   flags: ""
    runs-on: ${{ matrix.runner_label }}
    steps:
      - name: Test Workflows
        uses: comfy-org/comfy-action@main
        with:
          os: ${{ matrix.os }}
          python_version: ${{ matrix.python_version }}
          torch_version: ${{ matrix.torch_version }}
          google_credentials: ${{ secrets.GCS_SERVICE_ACCOUNT_JSON }}
          comfyui_flags: ${{ matrix.flags }}

  # test-win-nightly:
  #   strategy:
  #     fail-fast: true
  #     matrix:
  #       os: [windows]
  #       python_version: ["3.9", "3.10", "3.11", "3.12"]
  #       cuda_version: ["12.1"]
  #       torch_version: ["nightly"]
  #       include:
  #         - os: windows
  #           runner_label: [self-hosted, Windows]
  #           flags: ""
  #   runs-on: ${{ matrix.runner_label }}
  #   steps:
  #     - name: Test Workflows
  #       uses: comfy-org/comfy-action@main
  #       with:
  #         os: ${{ matrix.os }}
  #         python_version: ${{ matrix.python_version }}
  #         torch_version: ${{ matrix.torch_version }}
  #         google_credentials: ${{ secrets.GCS_SERVICE_ACCOUNT_JSON }}
  #         comfyui_flags: ${{ matrix.flags }}

  test-unix-nightly:
    strategy:
      fail-fast: false
      matrix:
        os: [macos, linux]
        python_version: ["3.11"]
        cuda_version: ["12.1"]
        torch_version: ["nightly"]
        include:
          - os: macos
            runner_label: [self-hosted, macOS]
            flags: "--use-pytorch-cross-attention"
          - os: linux
            runner_label: [self-hosted, Linux]
            flags: ""
    runs-on: ${{ matrix.runner_label }}
    steps:
      - name: Test Workflows
        uses: comfy-org/comfy-action@main
        with:
          os: ${{ matrix.os }}
          python_version: ${{ matrix.python_version }}
          torch_version: ${{ matrix.torch_version }}
          google_credentials: ${{ secrets.GCS_SERVICE_ACCOUNT_JSON }}
          comfyui_flags: ${{ matrix.flags }}
