# 1.2 ControlNet和LoRA技术

## 🎯 学习目标

深入理解ControlNet的控制机制和LoRA微调技术，掌握如何精确控制AI生图过程。

## 🎮 ControlNet：精确控制的艺术

### 什么是ControlNet？

ControlNet是一种为预训练扩散模型添加条件控制的技术，可以通过各种输入（边缘、深度、姿态等）精确控制生成过程。

### 核心思想

传统的文本到图像生成只能通过文本描述控制，ControlNet引入了额外的空间控制信号：

```
文本提示 + 控制图像 → 精确控制的生成结果
```

### ControlNet架构

#### 基本结构

ControlNet采用"零卷积"技术，确保训练初期不影响原模型：

```python
class ControlNet(nn.Module):
    def __init__(self, original_unet):
        super().__init__()
        # 复制原始U-Net的编码器
        self.control_model = copy.deepcopy(original_unet.encoder)
        
        # 零卷积层
        self.zero_convs = nn.ModuleList([
            zero_module(Conv2d(block_out_channels[i], block_out_channels[i], 1))
            for i in range(len(block_out_channels))
        ])
        
        # 控制输入处理
        self.controlnet_cond_embedding = ControlNetConditioningEmbedding(
            conditioning_embedding_channels=320,
            conditioning_channels=3,  # 根据控制类型调整
            block_out_channels=(16, 32, 96, 256),
        )

def zero_module(module):
    """将模块的参数初始化为零"""
    for p in module.parameters():
        p.detach().zero_()
    return module
```

#### 控制信号注入

```python
def forward(self, x, timestep, encoder_hidden_states, controlnet_cond):
    # 1. 处理控制条件
    controlnet_cond = self.controlnet_cond_embedding(controlnet_cond)
    
    # 2. 添加到输入
    x = x + controlnet_cond
    
    # 3. 通过控制网络
    down_block_res_samples = []
    for down_block in self.down_blocks:
        x = down_block(x, timestep, encoder_hidden_states)
        down_block_res_samples.append(x)
    
    # 4. 应用零卷积
    controlnet_down_block_res_samples = []
    for sample, zero_conv in zip(down_block_res_samples, self.zero_convs):
        controlnet_down_block_res_samples.append(zero_conv(sample))
    
    return controlnet_down_block_res_samples
```

### ControlNet类型详解

#### 1. Canny边缘控制

**用途**：通过边缘信息控制生成图像的结构

```python
import cv2
import numpy as np

def canny_preprocess(image, low_threshold=100, high_threshold=200):
    """Canny边缘检测预处理"""
    # 转换为灰度图
    gray = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2GRAY)
    
    # Canny边缘检测
    edges = cv2.Canny(gray, low_threshold, high_threshold)
    
    # 转换为3通道
    edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
    
    return edges
```

**应用场景**：
- 保持建筑物轮廓
- 控制人物姿态边缘
- 精确的结构控制

#### 2. Depth深度控制

**用途**：通过深度信息控制空间布局

```python
def depth_preprocess(image):
    """深度估计预处理"""
    from transformers import pipeline
    
    # 使用预训练的深度估计模型
    depth_estimator = pipeline('depth-estimation')
    depth = depth_estimator(image)['depth']
    
    # 归一化深度图
    depth = np.array(depth)
    depth = (depth - depth.min()) / (depth.max() - depth.min())
    depth = (depth * 255).astype(np.uint8)
    
    return depth
```

**应用场景**：
- 控制前景背景关系
- 保持空间层次
- 室内外场景布局

#### 3. OpenPose姿态控制

**用途**：通过人体姿态关键点控制人物生成

```python
def openpose_preprocess(image):
    """OpenPose姿态检测"""
    import mediapipe as mp
    
    mp_pose = mp.solutions.pose
    mp_drawing = mp.solutions.drawing_utils
    
    with mp_pose.Pose(static_image_mode=True) as pose:
        # 检测姿态
        results = pose.process(cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR))
        
        # 绘制姿态
        pose_image = np.zeros_like(np.array(image))
        if results.pose_landmarks:
            mp_drawing.draw_landmarks(
                pose_image, results.pose_landmarks, mp_pose.POSE_CONNECTIONS)
        
        return pose_image
```

**应用场景**：
- 人物动作控制
- 舞蹈姿态生成
- 运动场景创作

#### 4. Scribble涂鸦控制

**用途**：通过简单涂鸦控制生成内容

```python
def scribble_preprocess(image):
    """涂鸦预处理"""
    # 简单的边缘检测作为涂鸦
    gray = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2GRAY)
    edges = cv2.Canny(gray, 50, 150)
    
    # 膨胀操作使线条更粗
    kernel = np.ones((3,3), np.uint8)
    edges = cv2.dilate(edges, kernel, iterations=1)
    
    return cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
```

### ControlNet训练过程

#### 数据准备

```python
def prepare_controlnet_data(image_path, control_type):
    """准备ControlNet训练数据"""
    # 加载原始图像
    image = Image.open(image_path)
    
    # 生成控制条件
    if control_type == "canny":
        control_image = canny_preprocess(image)
    elif control_type == "depth":
        control_image = depth_preprocess(image)
    elif control_type == "pose":
        control_image = openpose_preprocess(image)
    
    return {
        "image": image,
        "control_image": control_image,
        "prompt": generate_caption(image)  # 自动生成或人工标注
    }
```

#### 训练循环

```python
def train_controlnet(controlnet, unet, vae, text_encoder, dataloader):
    optimizer = torch.optim.AdamW(controlnet.parameters(), lr=1e-5)
    
    for batch in dataloader:
        # 编码图像到潜在空间
        latents = vae.encode(batch["image"]).latent_dist.sample()
        latents = latents * 0.18215
        
        # 添加噪声
        noise = torch.randn_like(latents)
        timesteps = torch.randint(0, 1000, (latents.shape[0],))
        noisy_latents = noise_scheduler.add_noise(latents, noise, timesteps)
        
        # 编码文本
        text_embeddings = text_encoder(batch["prompt"])[0]
        
        # ControlNet前向传播
        down_block_res_samples = controlnet(
            noisy_latents, timesteps, text_embeddings, batch["control_image"]
        )
        
        # U-Net预测
        noise_pred = unet(
            noisy_latents, timesteps, text_embeddings,
            down_block_additional_residuals=down_block_res_samples
        ).sample
        
        # 计算损失
        loss = F.mse_loss(noise_pred, noise)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
```

## 🔧 LoRA：高效微调技术

### 什么是LoRA？

LoRA（Low-Rank Adaptation）是一种参数高效的微调技术，通过低秩矩阵分解大幅减少可训练参数。

### 核心原理

#### 低秩分解

对于权重矩阵 $W \in \mathbb{R}^{d \times k}$，LoRA将更新表示为：

```
W' = W + \Delta W = W + BA
```

其中：
- $B \in \mathbb{R}^{d \times r}$，$A \in \mathbb{R}^{r \times k}$
- $r << min(d, k)$ 是秩
- 只需训练 $A$ 和 $B$，参数量从 $d \times k$ 减少到 $r \times (d + k)$

#### LoRA实现

```python
class LoRALayer(nn.Module):
    def __init__(self, original_layer, rank=4, alpha=1.0):
        super().__init__()
        self.original_layer = original_layer
        self.rank = rank
        self.alpha = alpha
        
        # 获取原始层的维度
        if isinstance(original_layer, nn.Linear):
            in_features = original_layer.in_features
            out_features = original_layer.out_features
        elif isinstance(original_layer, nn.Conv2d):
            in_features = original_layer.in_channels
            out_features = original_layer.out_channels
        
        # LoRA矩阵
        self.lora_A = nn.Parameter(torch.randn(rank, in_features) * 0.01)
        self.lora_B = nn.Parameter(torch.zeros(out_features, rank))
        
        # 冻结原始参数
        for param in self.original_layer.parameters():
            param.requires_grad = False
    
    def forward(self, x):
        # 原始输出
        original_output = self.original_layer(x)
        
        # LoRA输出
        if isinstance(self.original_layer, nn.Linear):
            lora_output = F.linear(x, self.lora_B @ self.lora_A)
        elif isinstance(self.original_layer, nn.Conv2d):
            lora_weight = (self.lora_B @ self.lora_A).view(
                self.original_layer.out_channels, 
                self.original_layer.in_channels,
                1, 1
            )
            lora_output = F.conv2d(x, lora_weight)
        
        return original_output + self.alpha * lora_output
```

### LoRA应用到Stable Diffusion

#### 注意力层LoRA

```python
def apply_lora_to_attention(unet, rank=4):
    """将LoRA应用到U-Net的注意力层"""
    for name, module in unet.named_modules():
        if isinstance(module, Attention):
            # 对query, key, value, output投影应用LoRA
            module.to_q = LoRALayer(module.to_q, rank)
            module.to_k = LoRALayer(module.to_k, rank)
            module.to_v = LoRALayer(module.to_v, rank)
            module.to_out[0] = LoRALayer(module.to_out[0], rank)
```

#### LoRA训练

```python
def train_lora(model, dataloader, rank=4, alpha=1.0):
    # 应用LoRA
    apply_lora_to_attention(model, rank)
    
    # 只优化LoRA参数
    lora_params = []
    for module in model.modules():
        if isinstance(module, LoRALayer):
            lora_params.extend([module.lora_A, module.lora_B])
    
    optimizer = torch.optim.AdamW(lora_params, lr=1e-4)
    
    for batch in dataloader:
        # 标准的扩散模型训练循环
        loss = compute_diffusion_loss(model, batch)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
```

### LoRA的优势

1. **参数效率**：通常只需训练原模型1-2%的参数
2. **存储效率**：LoRA权重文件通常只有几MB到几十MB
3. **组合性**：可以组合多个LoRA适应不同风格
4. **可逆性**：可以轻松移除或替换LoRA

### LoRA类型

#### 1. 风格LoRA
- **用途**：学习特定艺术风格
- **训练数据**：同一风格的图像集合
- **应用**：动漫风格、油画风格、摄影风格等

#### 2. 角色LoRA
- **用途**：学习特定角色特征
- **训练数据**：同一角色的多张图像
- **应用**：虚拟角色、明星面孔、动漫角色等

#### 3. 概念LoRA
- **用途**：学习特定概念或物体
- **训练数据**：包含特定概念的图像
- **应用**：特殊服装、建筑风格、物品等

## 🔄 ControlNet + LoRA组合使用

### 协同工作原理

```python
def generate_with_controlnet_lora(
    prompt, control_image, controlnet, lora_weights, 
    controlnet_scale=1.0, lora_scale=1.0
):
    # 1. 加载LoRA权重
    load_lora_weights(unet, lora_weights, lora_scale)
    
    # 2. 编码文本和控制条件
    text_embeddings = encode_text(prompt)
    
    # 3. 初始化噪声
    latents = torch.randn(1, 4, 64, 64)
    
    # 4. 去噪循环
    for t in scheduler.timesteps:
        # ControlNet预测
        down_block_res_samples = controlnet(
            latents, t, text_embeddings, control_image
        )
        
        # 应用控制强度
        down_block_res_samples = [
            sample * controlnet_scale for sample in down_block_res_samples
        ]
        
        # U-Net预测（包含LoRA）
        noise_pred = unet(
            latents, t, text_embeddings,
            down_block_additional_residuals=down_block_res_samples
        )
        
        # 更新latents
        latents = scheduler.step(noise_pred, t, latents).prev_sample
    
    # 5. 解码
    image = vae.decode(latents).sample
    return image
```

## 📝 实践示例

### ComfyUI中的ControlNet节点

```python
# 在ComfyUI中使用ControlNet的典型工作流
class ControlNetWorkflow:
    def __init__(self):
        self.nodes = {
            "load_checkpoint": "CheckpointLoaderSimple",
            "load_controlnet": "ControlNetLoader", 
            "controlnet_apply": "ControlNetApply",
            "preprocess": "CannyEdgePreprocessor",  # 根据类型选择
            "text_encode": "CLIPTextEncode",
            "sampler": "KSampler",
            "vae_decode": "VAEDecode"
        }
    
    def create_workflow(self):
        return {
            "1": {
                "class_type": "CheckpointLoaderSimple",
                "inputs": {"ckpt_name": "model.safetensors"}
            },
            "2": {
                "class_type": "ControlNetLoader",
                "inputs": {"control_net_name": "control_canny.pth"}
            },
            "3": {
                "class_type": "CannyEdgePreprocessor", 
                "inputs": {
                    "image": ["input_image", 0],
                    "low_threshold": 100,
                    "high_threshold": 200
                }
            },
            "4": {
                "class_type": "ControlNetApply",
                "inputs": {
                    "conditioning": ["text_encode", 0],
                    "control_net": ["2", 0],
                    "image": ["3", 0],
                    "strength": 1.0
                }
            }
        }
```

## 🎯 小结

ControlNet和LoRA是现代AI生图的两大核心技术：

- **ControlNet**：提供精确的空间和结构控制
- **LoRA**：实现高效的风格和概念微调
- **组合使用**：可以同时获得精确控制和个性化风格

掌握这两项技术将大大提升你的AI生图能力和创作自由度。

## 🎯 练习题

1. **实践题**：使用不同类型的ControlNet（Canny、Depth、Pose）生成同一主题的图像，比较效果差异。

2. **编程题**：实现一个简单的LoRA层，并验证其参数量确实大幅减少。

3. **分析题**：分析ControlNet的"零卷积"设计的重要性，如果去掉会有什么影响？

---

**下一节**：[1.3 采样器和调度器](./03-samplers.md)
