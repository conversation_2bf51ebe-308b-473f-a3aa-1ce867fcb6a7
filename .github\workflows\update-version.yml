name: Update Version File

on:
  pull_request:
    paths:
      - "pyproject.toml"
    branches:
      - master

jobs:
  update-version:
    runs-on: ubuntu-latest
    # Don't run on fork PRs
    if: github.event.pull_request.head.repo.full_name == github.repository
    permissions:
      pull-requests: write
      contents: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip

      - name: Update comfyui_version.py
        run: |
          # Read version from pyproject.toml and update comfyui_version.py
          python -c '
          import tomllib

          # Read version from pyproject.toml
          with open("pyproject.toml", "rb") as f:
              config = tomllib.load(f)
              version = config["project"]["version"]

          # Write version to comfyui_version.py
          with open("comfyui_version.py", "w") as f:
              f.write("# This file is automatically generated by the build process when version is\n")
              f.write("# updated in pyproject.toml.\n")
              f.write(f"__version__ = \"{version}\"\n")
          '

      - name: Commit changes
        run: |
          git config --local user.name "github-actions"
          git config --local user.email "<EMAIL>"
          git fetch origin ${{ github.head_ref }}
          git checkout -B ${{ github.head_ref }} origin/${{ github.head_ref }}
          git add comfyui_version.py
          git diff --quiet && git diff --staged --quiet || git commit -m "chore: Update comfyui_version.py to match pyproject.toml"
          git push origin HEAD:${{ github.head_ref }}
