# 1.3 采样器和调度器

## 🎯 学习目标

深入理解各种采样算法和调度器的原理，掌握如何选择和调优采样参数以获得最佳生成效果。

## 🔬 采样器基础理论

### 什么是采样器？

采样器是扩散模型中负责从噪声逐步生成图像的算法。它决定了如何在每个时间步更新潜在表示，直接影响生成质量和速度。

### 采样过程的数学基础

#### DDPM原始采样

DDPM（Denoising Diffusion Probabilistic Models）的原始采样公式：

```
x_{t-1} = 1/√α_t * (x_t - (1-α_t)/√(1-ᾱ_t) * ε_θ(x_t, t)) + σ_t * z
```

其中：
- `x_t` 是当前时间步的潜在表示
- `ε_θ(x_t, t)` 是神经网络预测的噪声
- `α_t, ᾱ_t` 是噪声调度参数
- `σ_t` 是噪声方差
- `z ~ N(0, I)` 是随机噪声

#### DDIM确定性采样

DDIM（Denoising Diffusion Implicit Models）提供确定性采样：

```
x_{t-1} = √ᾱ_{t-1} * pred_x0 + √(1-ᾱ_{t-1}) * ε_θ(x_t, t)
```

其中 `pred_x0` 是预测的原始图像。

## 🚀 主流采样器详解

### 1. Euler采样器

#### 基本Euler方法

Euler采样器使用一阶欧拉方法求解ODE：

```python
def euler_step(x, t, dt, model):
    """Euler采样步骤"""
    # 预测噪声
    eps = model(x, t)
    
    # 计算导数
    dx_dt = (x - eps) / t
    
    # Euler更新
    x_next = x + dx_dt * dt
    
    return x_next
```

#### Euler Ancestral (EulerA)

添加随机性的Euler采样器：

```python
def euler_ancestral_step(x, t, dt, model, sigma):
    """Euler Ancestral采样步骤"""
    eps = model(x, t)
    
    # 确定性部分
    dx_dt = (x - eps) / t
    x_det = x + dx_dt * dt
    
    # 随机性部分
    if t > 0:
        noise = torch.randn_like(x)
        x_next = x_det + sigma * noise
    else:
        x_next = x_det
    
    return x_next
```

**特点**：
- 快速收敛
- 适合较少步数（10-30步）
- EulerA版本质量更高但不确定性

### 2. DPM++采样器

#### DPM++ 2M

DPM++是基于扩散概率模型的高阶求解器：

```python
class DPMPlusPlus2M:
    def __init__(self, model, noise_schedule):
        self.model = model
        self.noise_schedule = noise_schedule
        
    def step(self, x, t, prev_t):
        """DPM++ 2M采样步骤"""
        # 第一次预测
        eps1 = self.model(x, t)
        x1 = self.dpm_solver_first_order_update(x, t, prev_t, eps1)
        
        # 第二次预测
        eps2 = self.model(x1, prev_t)
        
        # 二阶更新
        x_next = self.dpm_solver_second_order_update(x, t, prev_t, eps1, eps2)
        
        return x_next
    
    def dpm_solver_second_order_update(self, x, t, prev_t, eps1, eps2):
        """二阶DPM求解器更新"""
        lambda_t = self.noise_schedule.marginal_lambda(t)
        lambda_prev = self.noise_schedule.marginal_lambda(prev_t)
        
        h = lambda_prev - lambda_t
        
        # 二阶Adams-Bashforth公式
        x_next = (
            self.noise_schedule.marginal_alpha(prev_t) / 
            self.noise_schedule.marginal_alpha(t) * x
            - self.noise_schedule.marginal_std(prev_t) * 
            (torch.expm1(h) * eps1 + torch.expm1(h) / h * (eps2 - eps1))
        )
        
        return x_next
```

**特点**：
- 高质量输出
- 适合中等步数（20-50步）
- 计算开销适中

### 3. DDIM采样器

#### 标准DDIM

```python
def ddim_step(x, t, prev_t, model, eta=0.0):
    """DDIM采样步骤"""
    # 预测噪声
    eps = model(x, t)
    
    # 预测原始图像
    alpha_t = noise_schedule.alphas_cumprod[t]
    alpha_prev = noise_schedule.alphas_cumprod[prev_t] if prev_t >= 0 else 1.0
    
    pred_x0 = (x - torch.sqrt(1 - alpha_t) * eps) / torch.sqrt(alpha_t)
    
    # DDIM更新
    direction = torch.sqrt(1 - alpha_prev) * eps
    x_next = torch.sqrt(alpha_prev) * pred_x0 + direction
    
    # 可选的随机性
    if eta > 0:
        sigma = eta * torch.sqrt((1 - alpha_prev) / (1 - alpha_t)) * torch.sqrt(1 - alpha_t / alpha_prev)
        noise = torch.randn_like(x)
        x_next += sigma * noise
    
    return x_next
```

**特点**：
- 确定性采样（eta=0时）
- 可以跳步采样
- 适合快速生成

### 4. UniPC采样器

UniPC是统一的预测-校正采样器：

```python
class UniPC:
    def __init__(self, model, noise_schedule, variant='bh2'):
        self.model = model
        self.noise_schedule = noise_schedule
        self.variant = variant
        
    def multistep_uni_pc_update(self, x, model_prev_list, t_prev_list, t):
        """多步UniPC更新"""
        if len(model_prev_list) == 0:
            return self.dpm_solver_first_order_update(x, t_prev_list[-1], t, model_prev_list[-1])
        elif len(model_prev_list) == 1:
            return self.singlestep_uni_pc_update_2(x, t_prev_list[-1], t, model_prev_list[-1])
        else:
            return self.multistep_uni_pc_update_3(x, model_prev_list, t_prev_list, t)
```

**特点**：
- 最新的高效采样器
- 在少步数下表现优异
- 适合快速高质量生成

## 📊 调度器详解

### 什么是调度器？

调度器（Scheduler）定义了噪声添加和去除的时间表，控制扩散过程的节奏。

### 主要调度器类型

#### 1. 线性调度器

```python
def linear_beta_schedule(timesteps, beta_start=0.0001, beta_end=0.02):
    """线性噪声调度"""
    return torch.linspace(beta_start, beta_end, timesteps)
```

#### 2. 余弦调度器

```python
def cosine_beta_schedule(timesteps, s=0.008):
    """余弦噪声调度"""
    steps = timesteps + 1
    x = torch.linspace(0, timesteps, steps)
    alphas_cumprod = torch.cos(((x / timesteps) + s) / (1 + s) * torch.pi * 0.5) ** 2
    alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
    betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
    return torch.clip(betas, 0.0001, 0.9999)
```

#### 3. Karras调度器

```python
def get_karras_sigmas(n, sigma_min=0.1, sigma_max=10, rho=7):
    """Karras噪声调度"""
    ramp = torch.linspace(0, 1, n)
    min_inv_rho = sigma_min ** (1 / rho)
    max_inv_rho = sigma_max ** (1 / rho)
    sigmas = (max_inv_rho + ramp * (min_inv_rho - max_inv_rho)) ** rho
    return sigmas
```

## ⚙️ 采样参数优化

### 关键参数说明

#### 1. 步数（Steps）
- **少步数（10-20）**：快速生成，质量可能较低
- **中等步数（20-50）**：平衡质量和速度
- **多步数（50+）**：高质量，但速度慢

#### 2. CFG Scale（Classifier-Free Guidance）
- **低值（1-5）**：更自然，但可能偏离提示
- **中值（7-12）**：平衡遵循度和自然度
- **高值（15+）**：严格遵循提示，但可能过饱和

#### 3. 种子（Seed）
- 控制随机性
- 相同种子产生相同结果（确定性采样器）

### 参数调优策略

```python
def optimize_sampling_params(prompt, base_params):
    """采样参数优化策略"""
    results = []
    
    # 测试不同步数
    for steps in [20, 30, 50]:
        # 测试不同CFG值
        for cfg in [7, 10, 15]:
            # 测试不同采样器
            for sampler in ['euler', 'dpm++_2m', 'ddim']:
                params = base_params.copy()
                params.update({
                    'steps': steps,
                    'cfg_scale': cfg,
                    'sampler': sampler
                })
                
                image = generate_image(prompt, params)
                score = evaluate_image_quality(image, prompt)
                
                results.append({
                    'params': params,
                    'score': score,
                    'image': image
                })
    
    # 返回最佳参数
    best_result = max(results, key=lambda x: x['score'])
    return best_result['params']
```

## 🎨 ComfyUI中的采样器使用

### KSampler节点

在ComfyUI中，采样器通过KSampler节点使用：

```python
# KSampler节点的典型配置
ksampler_config = {
    "seed": 42,
    "steps": 30,
    "cfg": 8.0,
    "sampler_name": "dpm++_2m",
    "scheduler": "karras",
    "denoise": 1.0
}
```

### 采样器选择指南

| 采样器 | 推荐步数 | 特点 | 适用场景 |
|--------|----------|------|----------|
| Euler | 10-30 | 快速，简单 | 快速预览 |
| EulerA | 15-30 | 高质量，随机性 | 一般生成 |
| DPM++ 2M | 20-50 | 高质量，稳定 | 精细作品 |
| DPM++ SDE | 20-40 | 高质量，多样性 | 创意生成 |
| DDIM | 10-50 | 确定性，快速 | 批量生成 |
| UniPC | 10-25 | 高效，新算法 | 快速高质量 |

## 🔬 高级采样技术

### 1. 分层采样

```python
def hierarchical_sampling(prompt, steps_coarse=10, steps_fine=20):
    """分层采样：先粗后细"""
    # 粗采样
    coarse_result = sample_with_params(prompt, {
        'steps': steps_coarse,
        'cfg': 5,
        'sampler': 'euler'
    })
    
    # 细采样
    fine_result = sample_with_params(prompt, {
        'steps': steps_fine,
        'cfg': 8,
        'sampler': 'dpm++_2m',
        'init_image': coarse_result,
        'denoise': 0.7
    })
    
    return fine_result
```

### 2. 自适应采样

```python
def adaptive_sampling(prompt, quality_threshold=0.8):
    """自适应采样：根据质量动态调整"""
    steps = 20
    max_steps = 100
    
    while steps <= max_steps:
        result = sample_with_params(prompt, {'steps': steps})
        quality = evaluate_quality(result)
        
        if quality >= quality_threshold:
            return result
        
        steps += 10
    
    return result
```

### 3. 集成采样

```python
def ensemble_sampling(prompt, num_samples=5):
    """集成采样：多次采样后选择最佳"""
    samples = []
    
    for i in range(num_samples):
        sample = sample_with_params(prompt, {
            'seed': i,
            'steps': 30,
            'sampler': 'dpm++_2m'
        })
        quality = evaluate_quality(sample)
        samples.append((sample, quality))
    
    # 返回质量最高的样本
    best_sample = max(samples, key=lambda x: x[1])
    return best_sample[0]
```

## 📊 性能对比

### 速度 vs 质量

| 采样器 | 相对速度 | 质量评分 | 推荐用途 |
|--------|----------|----------|----------|
| Euler | 100% | 7/10 | 快速预览 |
| EulerA | 95% | 8/10 | 日常使用 |
| DPM++ 2M | 70% | 9/10 | 高质量作品 |
| UniPC | 85% | 8.5/10 | 平衡选择 |
| DDIM | 90% | 7.5/10 | 批量生成 |

## 🎯 小结

采样器和调度器是控制AI生图质量和速度的关键组件：

- **采样器**：决定去噪算法，影响质量和速度
- **调度器**：控制噪声时间表，影响生成过程
- **参数调优**：根据需求平衡质量、速度和多样性
- **高级技术**：分层、自适应、集成采样提升效果

理解这些概念将帮助你在ComfyUI中做出最佳的采样选择。

## 🎯 练习题

1. **实验题**：使用相同提示和种子，比较不同采样器在不同步数下的效果。

2. **分析题**：解释为什么DPM++采样器通常比Euler采样器质量更高？

3. **优化题**：为一个特定的生成任务设计最优的采样参数组合。

---

**下一章**：[第二章：ComfyUI基础操作](../02-comfyui-basics/README.md)
